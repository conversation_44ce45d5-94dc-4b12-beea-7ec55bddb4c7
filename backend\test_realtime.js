const WebSocket = require('ws');

// Test Supabase Realtime WebSocket connection
const SUPABASE_URL = 'ws://localhost:54321/realtime/v1/websocket';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

console.log('Testing Supabase Realtime WebSocket connection...');
console.log('URL:', SUPABASE_URL);

const ws = new WebSocket(SUPABASE_URL, {
  headers: {
    'apikey': ANON_KEY,
    'Authorization': `Bearer ${ANON_KEY}`
  }
});

ws.on('open', function open() {
  console.log('✅ WebSocket connection opened successfully!');
  
  // Send a join message to subscribe to the realtime_test table
  const joinMessage = {
    topic: 'realtime:public:realtime_test',
    event: 'phx_join',
    payload: {},
    ref: '1'
  };
  
  console.log('📤 Sending join message:', JSON.stringify(joinMessage));
  ws.send(JSON.stringify(joinMessage));
  
  // Close connection after 5 seconds
  setTimeout(() => {
    console.log('🔌 Closing connection...');
    ws.close();
  }, 5000);
});

ws.on('message', function message(data) {
  console.log('📥 Received message:', data.toString());
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket error:', err.message);
});

ws.on('close', function close(code, reason) {
  console.log('🔌 WebSocket connection closed:', code, reason.toString());
  console.log('✅ Realtime test completed!');
});
